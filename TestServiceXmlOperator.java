import com.unimas.asn.util.ServiceXmlOperator;
import com.unimas.asn.bean.UdpConfig;

/**
 * 测试ServiceXmlOperator的新功能
 * 验证从Properties文件读取配置，displayname从XML文件读取
 */
public class TestServiceXmlOperator {
    public static void main(String[] args) {
        try {
            // 测试服务ID 11
            ServiceXmlOperator operator = new ServiceXmlOperator("11");
            
            // 加载配置
            UdpConfig config = operator.loadUdpConfig();
            
            // 打印配置信息
            System.out.println("=== 服务配置查询测试 ===");
            System.out.println("服务ID (从Properties): " + config.getSid());
            System.out.println("显示名称 (从XML): " + config.getDisplayname());
            System.out.println("代理IP (从Properties): " + config.getIpproxy());
            System.out.println("代理端口 (从Properties): " + config.getPortclient());
            System.out.println("IP范围 (从Properties): " + config.getIprange());
            System.out.println("车牌检查 (从Properties): " + config.getCarcheck());
            System.out.println("身份证检查 (从Properties): " + config.getIdcheck());
            System.out.println("CRC过滤 (从Properties): " + config.getCrcfilter());
            System.out.println("ASN过滤 (从Properties): " + config.getAsnfilter());
            System.out.println("未通过处理 (从Properties): " + config.getUnpassdeal());
            System.out.println("主机IP (从Properties): " + config.getHostip());
            System.out.println("主机端口 (从Properties): " + config.getHostport());
            System.out.println("审计 (从Properties): " + config.getAudit());
            
            System.out.println("\n=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
